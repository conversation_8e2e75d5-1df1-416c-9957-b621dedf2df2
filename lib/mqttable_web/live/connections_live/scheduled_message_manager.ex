defmodule MqttableWeb.ConnectionsLive.ScheduledMessageManager do
  @moduledoc """
  Manages scheduled message operations for the connections LiveView.

  This module handles:
  - Scheduled message creation, editing, and deletion
  - Scheduled message state synchronization with workers
  - Connection state updates for scheduled messages
  - Scheduled message validation and processing

  All functions follow functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  require Logger
  import Phoenix.Component, only: [assign: 3]

  alias Mqttable.ConnectionSets
  alias MqttableWeb.Utils.ConnectionHelpers
  alias Mqttable.MqttClient.Worker

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type connection_sets :: [map()]
  @type connection :: map()
  @type scheduled_message :: map()

  def handle_remove_scheduled_message(socket, client_id, index_str) do
    index = String.to_integer(index_str)

    updated_connection_sets =
      remove_scheduled_message_from_sets(
        socket.assigns.connection_sets,
        client_id,
        index
      )

    if updated_connection_sets != socket.assigns.connection_sets do
      # Update the connection sets in the server
      ConnectionSets.update(updated_connection_sets)

      # Update socket assigns first
      socket = update_socket_with_connection_sets(socket, updated_connection_sets)

      # Synchronize all scheduled messages with the worker
      sync_client_scheduled_messages(socket, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  def handle_add_scheduled_message(socket, client_id, scheduled_message) do
    updated_connection_sets =
      add_scheduled_message_to_sets(
        socket.assigns.connection_sets,
        client_id,
        scheduled_message
      )

    if updated_connection_sets != socket.assigns.connection_sets do
      # Update the connection sets in the server
      ConnectionSets.update(updated_connection_sets)

      # Update socket assigns first
      socket = update_socket_with_connection_sets(socket, updated_connection_sets)

      # Synchronize all scheduled messages with the worker
      sync_client_scheduled_messages(socket, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles updating an existing scheduled message.

  Updates the scheduled message in the connection state and synchronizes with the worker.
  """
  @spec handle_update_scheduled_message(socket(), String.t(), integer(), scheduled_message()) ::
          {:noreply, socket()}
  def handle_update_scheduled_message(socket, client_id, index, scheduled_message) do
    updated_connection_sets =
      update_scheduled_message_in_sets(
        socket.assigns.connection_sets,
        client_id,
        index,
        scheduled_message
      )

    if updated_connection_sets != socket.assigns.connection_sets do
      # Update the connection sets in the server
      ConnectionSets.update(updated_connection_sets)

      # Update socket assigns first
      socket = update_socket_with_connection_sets(socket, updated_connection_sets)

      # Synchronize all scheduled messages with the worker
      sync_client_scheduled_messages(socket, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Finds a connection and scheduled message by client ID and index.

  Returns a tuple containing the connection set, connection, and scheduled message.
  """
  @spec find_connection_and_scheduled_message(connection_sets(), String.t(), integer()) ::
          {map(), connection(), scheduled_message()} | nil
  def find_connection_and_scheduled_message(connection_sets, client_id, index) do
    Enum.find_value(connection_sets, fn connection_set ->
      case Enum.find(connection_set.connections, fn conn -> conn.client_id == client_id end) do
        nil ->
          nil

        connection ->
          scheduled_messages = Map.get(connection, :scheduled_messages, [])

          if index >= 0 && index < length(scheduled_messages) do
            scheduled_message = Enum.at(scheduled_messages, index)
            {connection_set, connection, scheduled_message}
          else
            nil
          end
      end
    end)
  end

  @doc """
  Validates scheduled message parameters.

  Ensures all required fields are present and valid.
  """
  @spec validate_scheduled_message(map()) :: {:ok, scheduled_message()} | {:error, String.t()}
  def validate_scheduled_message(params) do
    required_fields = ["topic", "payload", "qos", "interval"]

    case Enum.find(required_fields, fn field -> is_nil(params[field]) or params[field] == "" end) do
      nil ->
        with {:ok, qos} <- validate_qos(params["qos"]),
             {:ok, interval} <- validate_interval(params["interval"]) do
          {:ok,
           %{
             topic: String.trim(params["topic"]),
             payload: params["payload"],
             qos: qos,
             interval: interval,
             retain: Map.get(params, "retain", false),
             enabled: Map.get(params, "enabled", true),
             created_at: DateTime.utc_now(),
             last_sent_at: nil,
             send_count: 0
           }}
        else
          {:error, reason} -> {:error, reason}
        end

      missing_field ->
        {:error, "Missing or empty required field: #{missing_field}"}
    end
  end

  # Private functions

  @spec remove_scheduled_message_from_sets(connection_sets(), String.t(), integer()) ::
          connection_sets()
  defp remove_scheduled_message_from_sets(connection_sets, client_id, index) do
    Enum.map(connection_sets, fn connection_set ->
      updated_connections =
        Enum.map(connection_set.connections, fn connection ->
          if connection.client_id == client_id do
            current_scheduled_messages = Map.get(connection, :scheduled_messages, [])

            if index >= 0 && index < length(current_scheduled_messages) do
              updated_scheduled_messages = List.delete_at(current_scheduled_messages, index)
              Map.put(connection, :scheduled_messages, updated_scheduled_messages)
            else
              connection
            end
          else
            connection
          end
        end)

      Map.put(connection_set, :connections, updated_connections)
    end)
  end

  @spec add_scheduled_message_to_sets(connection_sets(), String.t(), scheduled_message()) ::
          connection_sets()
  defp add_scheduled_message_to_sets(connection_sets, client_id, scheduled_message) do
    Enum.map(connection_sets, fn connection_set ->
      updated_connections =
        Enum.map(connection_set.connections, fn connection ->
          if connection.client_id == client_id do
            current_scheduled_messages = Map.get(connection, :scheduled_messages, [])
            updated_scheduled_messages = current_scheduled_messages ++ [scheduled_message]
            Map.put(connection, :scheduled_messages, updated_scheduled_messages)
          else
            connection
          end
        end)

      Map.put(connection_set, :connections, updated_connections)
    end)
  end

  @spec update_scheduled_message_in_sets(
          connection_sets(),
          String.t(),
          integer(),
          scheduled_message()
        ) :: connection_sets()
  defp update_scheduled_message_in_sets(connection_sets, client_id, index, scheduled_message) do
    Enum.map(connection_sets, fn connection_set ->
      updated_connections =
        Enum.map(connection_set.connections, fn connection ->
          if connection.client_id == client_id do
            current_scheduled_messages = Map.get(connection, :scheduled_messages, [])

            if index >= 0 && index < length(current_scheduled_messages) do
              updated_scheduled_messages =
                List.replace_at(current_scheduled_messages, index, scheduled_message)

              Map.put(connection, :scheduled_messages, updated_scheduled_messages)
            else
              connection
            end
          else
            connection
          end
        end)

      Map.put(connection_set, :connections, updated_connections)
    end)
  end

  @spec find_connection_by_client_id(connection_sets(), String.t()) :: connection() | nil
  defp find_connection_by_client_id(connection_sets, client_id) do
    Enum.find_value(connection_sets, fn connection_set ->
      Enum.find(connection_set.connections, fn connection ->
        connection.client_id == client_id
      end)
    end)
  end

  defp sync_client_scheduled_messages(socket, client_id) do
    # Find the connection and get its scheduled messages
    connection = find_connection_by_client_id(socket.assigns.connection_sets, client_id)
    scheduled_messages = Map.get(connection, :scheduled_messages, [])

    # Sync with the worker
    Worker.sync_scheduled_messages(
      socket.assigns.active_connection_set.name,
      client_id,
      scheduled_messages
    )

    :ok
  end

  @spec update_socket_with_connection_sets(socket(), connection_sets()) :: socket()
  defp update_socket_with_connection_sets(socket, updated_connection_sets) do
    active_set_name =
      if socket.assigns.active_connection_set,
        do: socket.assigns.active_connection_set.name,
        else: nil

    updated_active_set =
      if active_set_name do
        ConnectionHelpers.find_connection_set_by_name(updated_connection_sets, active_set_name)
      else
        nil
      end

    socket
    |> assign(:connection_sets, updated_connection_sets)
    |> assign(:active_connection_set, updated_active_set)
  end

  @spec validate_qos(any()) :: {:ok, integer()} | {:error, String.t()}
  defp validate_qos(qos) when is_integer(qos) and qos in [0, 1, 2], do: {:ok, qos}

  defp validate_qos(qos) when is_binary(qos) do
    case Integer.parse(qos) do
      {parsed_qos, ""} when parsed_qos in [0, 1, 2] -> {:ok, parsed_qos}
      _ -> {:error, "QoS must be 0, 1, or 2"}
    end
  end

  defp validate_qos(_), do: {:error, "Invalid QoS value"}

  @spec validate_interval(any()) :: {:ok, integer()} | {:error, String.t()}
  defp validate_interval(interval) when is_integer(interval) and interval > 0, do: {:ok, interval}

  defp validate_interval(interval) when is_binary(interval) do
    case Integer.parse(interval) do
      {parsed_interval, ""} when parsed_interval > 0 -> {:ok, parsed_interval}
      _ -> {:error, "Interval must be a positive integer"}
    end
  end

  defp validate_interval(_), do: {:error, "Invalid interval value"}

  @doc """
  Formats scheduled message interval for display.

  Converts seconds to human-readable format.
  """
  @spec format_interval(integer()) :: String.t()
  def format_interval(seconds) when is_integer(seconds) and seconds > 0 do
    cond do
      seconds < 60 ->
        "#{seconds}s"

      seconds < 3600 ->
        minutes = div(seconds, 60)
        remaining_seconds = rem(seconds, 60)

        if remaining_seconds == 0 do
          "#{minutes}m"
        else
          "#{minutes}m #{remaining_seconds}s"
        end

      seconds < 86400 ->
        hours = div(seconds, 3600)
        remaining_minutes = div(rem(seconds, 3600), 60)

        if remaining_minutes == 0 do
          "#{hours}h"
        else
          "#{hours}h #{remaining_minutes}m"
        end

      true ->
        days = div(seconds, 86400)
        remaining_hours = div(rem(seconds, 86400), 3600)

        if remaining_hours == 0 do
          "#{days}d"
        else
          "#{days}d #{remaining_hours}h"
        end
    end
  end

  def format_interval(_), do: "Invalid"

  @doc """
  Calculates the next scheduled send time for a message.

  Returns the next DateTime when the message should be sent.
  """
  @spec calculate_next_send_time(scheduled_message()) :: DateTime.t()
  def calculate_next_send_time(%{last_sent_at: nil, interval: interval}) do
    DateTime.add(DateTime.utc_now(), interval, :second)
  end

  def calculate_next_send_time(%{last_sent_at: last_sent_at, interval: interval}) do
    DateTime.add(last_sent_at, interval, :second)
  end

  @doc """
  Checks if a scheduled message is due to be sent.

  Returns true if the message should be sent now, false otherwise.
  """
  @spec is_message_due?(scheduled_message()) :: boolean()
  def is_message_due?(%{enabled: false}), do: false
  def is_message_due?(%{last_sent_at: nil}), do: true

  def is_message_due?(message) do
    next_send_time = calculate_next_send_time(message)
    DateTime.compare(DateTime.utc_now(), next_send_time) != :lt
  end
end
